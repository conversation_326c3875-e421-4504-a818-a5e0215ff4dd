package examples

import (
	"fmt"
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/services"
	core "gitlab.finema.co/finema/idin-core"
)

// Example functions showing how to work with phase_id from CMS projects

// GetCurrentPhaseIDFromProject demonstrates how to get the current phase_id from a project
func GetCurrentPhaseIDFromProject(ctx core.IContext, projectID string) (*string, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)
	
	// Get the project
	project, err := cmsProjectSvc.Find(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to find project: %v", err)
	}
	
	// Return the current phase_id (could be nil)
	return project.PhaseID, nil
}

// FindProjectsWithSamePhase demonstrates how to find all projects with the same current phase
func FindProjectsWithSamePhase(ctx core.IContext, referenceProjectID string) ([]*models.CMSProject, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)
	
	// First, get the reference project's phase_id
	referenceProject, err := cmsProjectSvc.Find(referenceProjectID)
	if err != nil {
		return nil, fmt.Errorf("failed to find reference project: %v", err)
	}
	
	// If the reference project doesn't have a current phase, return empty
	if referenceProject.PhaseID == nil {
		return []*models.CMSProject{}, nil
	}
	
	// Create filters to find projects with the same phase_id
	filters := &services.CMSProjectFilters{
		PhaseID: *referenceProject.PhaseID,
	}
	
	// Get projects with the same phase
	pageOptions := &core.PageOptions{
		Page:  1,
		Limit: 100, // Adjust as needed
	}
	
	result, err := cmsProjectSvc.Pagination(filters, pageOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to get projects with same phase: %v", err)
	}
	
	return result.Data, nil
}

// CheckIfProjectHasCurrentPhase demonstrates how to check if a project has a current phase
func CheckIfProjectHasCurrentPhase(ctx core.IContext, projectID string) (bool, *models.CMSProjectPhase, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)
	
	// Get the project with relations (including Phase)
	project, err := cmsProjectSvc.Find(projectID)
	if err != nil {
		return false, nil, fmt.Errorf("failed to find project: %v", err)
	}
	
	// Check if project has a current phase
	if project.PhaseID == nil {
		return false, nil, nil
	}
	
	// If Phase relation is loaded, return it
	if project.Phase != nil {
		return true, project.Phase, nil
	}
	
	// If Phase relation is not loaded, we know it exists but don't have details
	return true, nil, nil
}

// FilterProjectsByPhaseAndStatus demonstrates combining phase_id filter with other filters
func FilterProjectsByPhaseAndStatus(ctx core.IContext, phaseID string, status []string) ([]*models.CMSProject, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)
	
	// Create filters combining phase_id and status
	filters := &services.CMSProjectFilters{
		PhaseID: phaseID,
		Status:  status,
	}
	
	pageOptions := &core.PageOptions{
		Page:  1,
		Limit: 50,
	}
	
	result, err := cmsProjectSvc.Pagination(filters, pageOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to filter projects: %v", err)
	}
	
	return result.Data, nil
}

// GetProjectCurrentPhaseDetails demonstrates how to get full current phase details
func GetProjectCurrentPhaseDetails(ctx core.IContext, projectID string) (*models.CMSProjectPhase, error) {
	cmsProjectSvc := services.NewCMSProjectService(ctx)
	cmsProjectPhaseSvc := services.NewCMSProjectPhaseService(ctx)
	
	// Get the project
	project, err := cmsProjectSvc.Find(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to find project: %v", err)
	}
	
	// Check if project has a current phase
	if project.PhaseID == nil {
		return nil, fmt.Errorf("project does not have a current phase")
	}
	
	// Get the full phase details
	phase, err := cmsProjectPhaseSvc.Find(*project.PhaseID)
	if err != nil {
		return nil, fmt.Errorf("failed to find current phase: %v", err)
	}
	
	return phase, nil
}

// Example usage in a controller or service method:
/*
func ExampleControllerMethod(c core.IHTTPContext) error {
	projectID := c.Param("id")
	
	// Get current phase_id
	phaseID, err := GetCurrentPhaseIDFromProject(c, projectID)
	if err != nil {
		return c.JSON(500, map[string]string{"error": err.Error()})
	}
	
	if phaseID == nil {
		return c.JSON(200, map[string]interface{}{
			"project_id": projectID,
			"phase_id":   nil,
			"message":    "Project does not have a current phase",
		})
	}
	
	// Find other projects with the same phase
	similarProjects, err := FindProjectsWithSamePhase(c, projectID)
	if err != nil {
		return c.JSON(500, map[string]string{"error": err.Error()})
	}
	
	return c.JSON(200, map[string]interface{}{
		"project_id":       projectID,
		"current_phase_id": *phaseID,
		"similar_projects": similarProjects,
	})
}
*/
